#!/bin/bash

echo "HNSW 索引构建进度监控工具"
echo "================================"

# 检查日志文件是否存在
if [ ! -f "hnsw_build.log" ]; then
    echo "❌ 日志文件 hnsw_build.log 不存在"
    echo "请确保脚本正在后台运行"
    exit 1
fi

echo "📊 最新进度信息："
echo "--------------------------------"

# 显示最新的进度行
tail -n 20 hnsw_build.log | grep "Progress:" | tail -n 1

echo ""
echo "💾 检查点文件："
echo "--------------------------------"
ls -lah hnsw_10m_index_*checkpoint*.pkl | tail -n 3

echo ""
echo "📈 实时监控 (按 Ctrl+C 退出)："
echo "--------------------------------"

# 实时监控进度
tail -f hnsw_build.log | grep --line-buffered "Progress:\|Saving checkpoint\|Index saved successfully"
