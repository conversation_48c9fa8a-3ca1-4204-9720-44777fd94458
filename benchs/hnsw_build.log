nohup: ignoring input
Persistent 10M HNSW Implementation
============================================================

Parameter Configuration:
1. Standard config: M=16, ef_construction=200 (faster build, ~20h)
2. High quality config: M=30, ef_construction=360 (better quality, ~30-40h)
3. Custom config
Choose configuration (1/2/3): Traceback (most recent call last):
  File "persistent_10m_hnsw.py", line 528, in <module>
    sys.exit(main())
  File "persistent_10m_hnsw.py", line 393, in main
    config_choice = input("Choose configuration (1/2/3): ").strip()
OSError: [Errno 9] Bad file descriptor
